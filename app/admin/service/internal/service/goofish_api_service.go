package service

import (
	"context"

	"github.com/go-kratos/kratos/v2/log"

	adminV1 "kratos-admin/api/gen/go/admin/service/v1"
	goofishV1 "kratos-admin/api/gen/go/goofish/service/v1"
)

type GoofishApiService struct {
	adminV1.GoofishApiHTTPServer

	log *log.Helper
}

func NewGoofishApiService(logger log.Logger) *GoofishApiService {
	l := log.NewHelper(log.With(logger, "module", "goofish-api/service/admin-service"))
	return &GoofishApiService{
		log: l,
	}
}

// GetPlatformInfo 查询平台信息
func (s *GoofishApiService) GetPlatformInfo(ctx context.Context, req *goofishV1.PlatformInfoRequest) (*goofishV1.PlatformInfoResponse, error) {
	s.log.WithContext(ctx).Infof("GetPlatformInfo called")

	// 返回模拟数据
	return &goofishV1.PlatformInfoResponse{
		Code: 200,
		Msg:  "success",
		Data: &goofishV1.PlatformInfoData{
			AppId: 1173028205413829,
		},
	}, nil
}

// GetUserInfo 查询商户信息
func (s *GoofishApiService) GetUserInfo(ctx context.Context, req *goofishV1.UserInfoRequest) (*goofishV1.UserInfoResponse, error) {
	s.log.WithContext(ctx).Infof("GetUserInfo called")

	// 返回模拟数据
	return &goofishV1.UserInfoResponse{
		Code: 200,
		Msg:  "success",
		Data: &goofishV1.UserInfoData{
			Balance: 100000, // 余额：1000.00 元（以分为单位）
		},
	}, nil
}

// GetGoodsList 查询商品列表
func (s *GoofishApiService) GetGoodsList(ctx context.Context, req *goofishV1.GoodsListRequest) (*goofishV1.GoodsListResponse, error) {
	s.log.WithContext(ctx).Infof("GetGoodsList called with keyword: %s, goods_type: %d, page_no: %d, page_size: %d",
		req.Keyword, req.GoodsType, req.PageNo, req.PageSize)

	// 返回模拟数据
	goods := []*goofishV1.GoodsDetail{
		{
			GoodsNo:    "GOODS001",
			GoodsType:  1,
			GoodsName:  "测试商品1",
			Price:      1000, // 10.00 元
			Stock:      100,
			Status:     1,
			UpdateTime: **********, // 2022-01-01 00:00:00
			Template: []*goofishV1.GoodsTemplate{
				{
					Code:  "account",
					Name:  "账号",
					Desc:  "游戏账号",
					Check: 1,
				},
			},
		},
		{
			GoodsNo:    "GOODS002",
			GoodsType:  1,
			GoodsName:  "测试商品2",
			Price:      2000, // 20.00 元
			Stock:      50,
			Status:     1,
			UpdateTime: **********,
			Template: []*goofishV1.GoodsTemplate{
				{
					Code:  "account",
					Name:  "账号",
					Desc:  "游戏账号",
					Check: 1,
				},
			},
		},
	}

	return &goofishV1.GoodsListResponse{
		Code: 200,
		Msg:  "success",
		Data: &goofishV1.GoodsListData{
			List:  goods,
			Count: int32(len(goods)),
		},
	}, nil
}

// GetGoodsDetail 查询商品详情
func (s *GoofishApiService) GetGoodsDetail(ctx context.Context, req *goofishV1.GoodsDetailRequest) (*goofishV1.GoodsDetailResponse, error) {
	s.log.WithContext(ctx).Infof("GetGoodsDetail called with goods_type: %d, goods_no: %s",
		req.GoodsType, req.GoodsNo)

	// 返回模拟数据
	return &goofishV1.GoodsDetailResponse{
		Code: 200,
		Msg:  "success",
		Data: &goofishV1.GoodsDetail{
			GoodsNo:    req.GoodsNo,
			GoodsType:  req.GoodsType,
			GoodsName:  "测试商品详情",
			Price:      1500, // 15.00 元
			Stock:      80,
			Status:     1,
			UpdateTime: **********,
			Template: []*goofishV1.GoodsTemplate{
				{
					Code:  "account",
					Name:  "账号",
					Desc:  "游戏账号",
					Check: 1,
				},
				{
					Code:  "password",
					Name:  "密码",
					Desc:  "游戏密码",
					Check: 1,
				},
			},
		},
	}, nil
}

// GetGoodsChangeSubscribeList 查询商品订阅列表
func (s *GoofishApiService) GetGoodsChangeSubscribeList(ctx context.Context, req *goofishV1.GoodsChangeSubscribeListRequest) (*goofishV1.GoodsChangeSubscribeListResponse, error) {
	s.log.WithContext(ctx).Infof("GetGoodsChangeSubscribeList called")

	// 返回模拟数据
	return &goofishV1.GoodsChangeSubscribeListResponse{
		Code: 200,
		Msg:  "success",
		Data: &goofishV1.GoodsChangeSubscribeListData{
			List:  []*goofishV1.GoodsChangeSubscribeListItem{},
			Count: 0,
		},
	}, nil
}

// GoodsChangeSubscribe 订阅商品变更通知
func (s *GoofishApiService) GoodsChangeSubscribe(ctx context.Context, req *goofishV1.GoodsChangeSubscribeRequest) (*goofishV1.GoodsChangeSubscribeResponse, error) {
	s.log.WithContext(ctx).Infof("GoodsChangeSubscribe called with goods_type: %d, goods_no: %s",
		req.GoodsType, req.GoodsNo)

	return &goofishV1.GoodsChangeSubscribeResponse{
		Code: 200,
		Msg:  "订阅成功",
	}, nil
}

// GoodsChangeUnsubscribe 取消商品变更通知
func (s *GoofishApiService) GoodsChangeUnsubscribe(ctx context.Context, req *goofishV1.GoodsChangeUnsubscribeRequest) (*goofishV1.GoodsChangeUnsubscribeResponse, error) {
	s.log.WithContext(ctx).Infof("GoodsChangeUnsubscribe called with goods_type: %d, goods_no: %s",
		req.GoodsType, req.GoodsNo)

	return &goofishV1.GoodsChangeUnsubscribeResponse{
		Code: 200,
		Msg:  "取消订阅成功",
	}, nil
}

// GoodsCallback 商品回调通知
func (s *GoofishApiService) GoodsCallback(ctx context.Context, req *goofishV1.GoodsCallbackRequest) (*goofishV1.GoodsCallbackResponse, error) {
	s.log.WithContext(ctx).Infof("GoodsCallback called with token: %s, items count: %d",
		req.Token, len(req.Items))

	return &goofishV1.GoodsCallbackResponse{
		Code: 200,
		Msg:  "回调处理成功",
	}, nil
}

// CreateRechargeOrder 创建直充订单
func (s *GoofishApiService) CreateRechargeOrder(ctx context.Context, req *goofishV1.CreateRechargeOrderRequest) (*goofishV1.CreateRechargeOrderResponse, error) {
	s.log.WithContext(ctx).Infof("CreateRechargeOrder called with goods_no: %s", req.GoodsNo)

	return &goofishV1.CreateRechargeOrderResponse{
		Code: 200,
		Msg:  "订单创建成功",
		Data: &goofishV1.RechargeOrderData{
			OrderNo:     "ORDER" + req.BizOrderNo,
			OutOrderNo:  req.BizOrderNo,
			OrderStatus: 1,
			OrderAmount: req.MaxAmount,
			GoodsName:   "测试商品",
			OrderTime:   **********,
			EndTime:     ********** + 3600,
			Remark:      "直充订单创建成功",
		},
	}, nil
}

// CreateCardOrder 创建卡密订单
func (s *GoofishApiService) CreateCardOrder(ctx context.Context, req *goofishV1.CreateCardOrderRequest) (*goofishV1.CreateCardOrderResponse, error) {
	s.log.WithContext(ctx).Infof("CreateCardOrder called with goods_no: %s", req.GoodsNo)

	return &goofishV1.CreateCardOrderResponse{
		Code: 200,
		Msg:  "订单创建成功",
		Data: &goofishV1.CardOrderData{
			OrderNo:     "CARD" + req.BizOrderNo,
			OutOrderNo:  req.BizOrderNo,
			OrderStatus: 1,
			OrderAmount: req.MaxAmount,
			OrderTime:   **********,
			EndTime:     ********** + 3600,
			CardItems: []*goofishV1.CardItem{
				{
					CardNo:  "CARD001",
					CardPwd: "PWD001",
				},
			},
			Remark: "卡密订单创建成功",
		},
	}, nil
}

// GetOrderDetail 查询订单详情
func (s *GoofishApiService) GetOrderDetail(ctx context.Context, req *goofishV1.OrderDetailRequest) (*goofishV1.OrderDetailResponse, error) {
	s.log.WithContext(ctx).Infof("GetOrderDetail called with order_type: %d, order_no: %s",
		req.OrderType, req.OrderNo)

	return &goofishV1.OrderDetailResponse{
		Code: 200,
		Msg:  "success",
		Data: &goofishV1.OrderDetailData{
			OrderNo:     req.OrderNo,
			OutOrderNo:  req.OutOrderNo,
			OrderStatus: 1,
			OrderAmount: 1000,
			OrderTime:   **********,
			EndTime:     ********** + 3600,
			Remark:      "订单详情",
		},
	}, nil
}

// OrderCallback 订单回调通知
func (s *GoofishApiService) OrderCallback(ctx context.Context, req *goofishV1.OrderCallbackRequest) (*goofishV1.OrderCallbackResponse, error) {
	s.log.WithContext(ctx).Infof("OrderCallback called with token: %s", req.Token)

	return &goofishV1.OrderCallbackResponse{
		Code: 200,
		Msg:  "订单回调处理成功",
	}, nil
}
