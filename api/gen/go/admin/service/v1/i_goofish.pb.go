// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        (unknown)
// source: admin/service/v1/i_goofish.proto

package servicev1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	v1 "kratos-admin/api/gen/go/goofish/service/v1"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_admin_service_v1_i_goofish_proto protoreflect.FileDescriptor

const file_admin_service_v1_i_goofish_proto_rawDesc = "" +
	"\n" +
	" admin/service/v1/i_goofish.proto\x12\x10admin.service.v1\x1a goofish/service/v1/goofish.proto\x1a\x1cgoogle/api/annotations.proto2\xe4\x10\n" +
	"\n" +
	"GoofishApi\x12\xa5\x01\n" +
	"\x0fGetPlatformInfo\x12\x1f.goofish.v1.PlatformInfoRequest\x1a .goofish.v1.PlatformInfoResponse\"O\x82\xd3\xe4\x93\x02I:\x01*\"D/goofish/open/info?mch_id={mch_id}&timestamp={timestamp}&sign={sign}\x12\x99\x01\n" +
	"\vGetUserInfo\x12\x1b.goofish.v1.UserInfoRequest\x1a\x1c.goofish.v1.UserInfoResponse\"O\x82\xd3\xe4\x93\x02I:\x01*\"D/goofish/user/info?mch_id={mch_id}&timestamp={timestamp}&sign={sign}\x12\x9d\x01\n" +
	"\fGetGoodsList\x12\x1c.goofish.v1.GoodsListRequest\x1a\x1d.goofish.v1.GoodsListResponse\"P\x82\xd3\xe4\x93\x02J:\x01*\"E/goofish/goods/list?mch_id={mch_id}&timestamp={timestamp}&sign={sign}\x12\xa5\x01\n" +
	"\x0eGetGoodsDetail\x12\x1e.goofish.v1.GoodsDetailRequest\x1a\x1f.goofish.v1.GoodsDetailResponse\"R\x82\xd3\xe4\x93\x02L:\x01*\"G/goofish/goods/detail?mch_id={mch_id}&timestamp={timestamp}&sign={sign}\x12\xdb\x01\n" +
	"\x1bGetGoodsChangeSubscribeList\x12+.goofish.v1.GoodsChangeSubscribeListRequest\x1a,.goofish.v1.GoodsChangeSubscribeListResponse\"a\x82\xd3\xe4\x93\x02[:\x01*\"V/goofish/goods/change/subscribe/list?mch_id={mch_id}&timestamp={timestamp}&sign={sign}\x12\xc7\x01\n" +
	"\x14GoodsChangeSubscribe\x12'.goofish.v1.GoodsChangeSubscribeRequest\x1a(.goofish.v1.GoodsChangeSubscribeResponse\"\\\x82\xd3\xe4\x93\x02V:\x01*\"Q/goofish/goods/change/subscribe?mch_id={mch_id}&timestamp={timestamp}&sign={sign}\x12\xcf\x01\n" +
	"\x16GoodsChangeUnsubscribe\x12).goofish.v1.GoodsChangeUnsubscribeRequest\x1a*.goofish.v1.GoodsChangeUnsubscribeResponse\"^\x82\xd3\xe4\x93\x02X:\x01*\"S/goofish/goods/change/unsubscribe?mch_id={mch_id}&timestamp={timestamp}&sign={sign}\x12\xc3\x01\n" +
	"\x13CreateRechargeOrder\x12&.goofish.v1.CreateRechargeOrderRequest\x1a'.goofish.v1.CreateRechargeOrderResponse\"[\x82\xd3\xe4\x93\x02U:\x01*\"P/goofish/order/recharge/create?mch_id={mch_id}&timestamp={timestamp}&sign={sign}\x12\xb7\x01\n" +
	"\x0fCreateCardOrder\x12\".goofish.v1.CreateCardOrderRequest\x1a#.goofish.v1.CreateCardOrderResponse\"[\x82\xd3\xe4\x93\x02U:\x01*\"P/goofish/order/purchase/create?mch_id={mch_id}&timestamp={timestamp}&sign={sign}\x12\xa5\x01\n" +
	"\x0eGetOrderDetail\x12\x1e.goofish.v1.OrderDetailRequest\x1a\x1f.goofish.v1.OrderDetailResponse\"R\x82\xd3\xe4\x93\x02L:\x01*\"G/goofish/order/detail?mch_id={mch_id}&timestamp={timestamp}&sign={sign}\x12\x94\x01\n" +
	"\rGoodsCallback\x12 .goofish.v1.GoodsCallbackRequest\x1a!.goofish.v1.GoodsCallbackResponse\">\x82\xd3\xe4\x93\x028:\x05items\"//api/open/callback/virtual/goods/notify/{token}\x12\x90\x01\n" +
	"\rOrderCallback\x12 .goofish.v1.OrderCallbackRequest\x1a!.goofish.v1.OrderCallbackResponse\":\x82\xd3\xe4\x93\x024:\x01*\"//api/open/callback/virtual/order/notify/{token}B\xbb\x01\n" +
	"\x14com.admin.service.v1B\rIGoofishProtoP\x01Z2kratos-admin/api/gen/go/admin/service/v1;servicev1\xa2\x02\x03ASX\xaa\x02\x10Admin.Service.V1\xca\x02\x10Admin\\Service\\V1\xe2\x02\x1cAdmin\\Service\\V1\\GPBMetadata\xea\x02\x12Admin::Service::V1b\x06proto3"

var file_admin_service_v1_i_goofish_proto_goTypes = []any{
	(*v1.PlatformInfoRequest)(nil),              // 0: goofish.v1.PlatformInfoRequest
	(*v1.UserInfoRequest)(nil),                  // 1: goofish.v1.UserInfoRequest
	(*v1.GoodsListRequest)(nil),                 // 2: goofish.v1.GoodsListRequest
	(*v1.GoodsDetailRequest)(nil),               // 3: goofish.v1.GoodsDetailRequest
	(*v1.GoodsChangeSubscribeListRequest)(nil),  // 4: goofish.v1.GoodsChangeSubscribeListRequest
	(*v1.GoodsChangeSubscribeRequest)(nil),      // 5: goofish.v1.GoodsChangeSubscribeRequest
	(*v1.GoodsChangeUnsubscribeRequest)(nil),    // 6: goofish.v1.GoodsChangeUnsubscribeRequest
	(*v1.CreateRechargeOrderRequest)(nil),       // 7: goofish.v1.CreateRechargeOrderRequest
	(*v1.CreateCardOrderRequest)(nil),           // 8: goofish.v1.CreateCardOrderRequest
	(*v1.OrderDetailRequest)(nil),               // 9: goofish.v1.OrderDetailRequest
	(*v1.GoodsCallbackRequest)(nil),             // 10: goofish.v1.GoodsCallbackRequest
	(*v1.OrderCallbackRequest)(nil),             // 11: goofish.v1.OrderCallbackRequest
	(*v1.PlatformInfoResponse)(nil),             // 12: goofish.v1.PlatformInfoResponse
	(*v1.UserInfoResponse)(nil),                 // 13: goofish.v1.UserInfoResponse
	(*v1.GoodsListResponse)(nil),                // 14: goofish.v1.GoodsListResponse
	(*v1.GoodsDetailResponse)(nil),              // 15: goofish.v1.GoodsDetailResponse
	(*v1.GoodsChangeSubscribeListResponse)(nil), // 16: goofish.v1.GoodsChangeSubscribeListResponse
	(*v1.GoodsChangeSubscribeResponse)(nil),     // 17: goofish.v1.GoodsChangeSubscribeResponse
	(*v1.GoodsChangeUnsubscribeResponse)(nil),   // 18: goofish.v1.GoodsChangeUnsubscribeResponse
	(*v1.CreateRechargeOrderResponse)(nil),      // 19: goofish.v1.CreateRechargeOrderResponse
	(*v1.CreateCardOrderResponse)(nil),          // 20: goofish.v1.CreateCardOrderResponse
	(*v1.OrderDetailResponse)(nil),              // 21: goofish.v1.OrderDetailResponse
	(*v1.GoodsCallbackResponse)(nil),            // 22: goofish.v1.GoodsCallbackResponse
	(*v1.OrderCallbackResponse)(nil),            // 23: goofish.v1.OrderCallbackResponse
}
var file_admin_service_v1_i_goofish_proto_depIdxs = []int32{
	0,  // 0: admin.service.v1.GoofishApi.GetPlatformInfo:input_type -> goofish.v1.PlatformInfoRequest
	1,  // 1: admin.service.v1.GoofishApi.GetUserInfo:input_type -> goofish.v1.UserInfoRequest
	2,  // 2: admin.service.v1.GoofishApi.GetGoodsList:input_type -> goofish.v1.GoodsListRequest
	3,  // 3: admin.service.v1.GoofishApi.GetGoodsDetail:input_type -> goofish.v1.GoodsDetailRequest
	4,  // 4: admin.service.v1.GoofishApi.GetGoodsChangeSubscribeList:input_type -> goofish.v1.GoodsChangeSubscribeListRequest
	5,  // 5: admin.service.v1.GoofishApi.GoodsChangeSubscribe:input_type -> goofish.v1.GoodsChangeSubscribeRequest
	6,  // 6: admin.service.v1.GoofishApi.GoodsChangeUnsubscribe:input_type -> goofish.v1.GoodsChangeUnsubscribeRequest
	7,  // 7: admin.service.v1.GoofishApi.CreateRechargeOrder:input_type -> goofish.v1.CreateRechargeOrderRequest
	8,  // 8: admin.service.v1.GoofishApi.CreateCardOrder:input_type -> goofish.v1.CreateCardOrderRequest
	9,  // 9: admin.service.v1.GoofishApi.GetOrderDetail:input_type -> goofish.v1.OrderDetailRequest
	10, // 10: admin.service.v1.GoofishApi.GoodsCallback:input_type -> goofish.v1.GoodsCallbackRequest
	11, // 11: admin.service.v1.GoofishApi.OrderCallback:input_type -> goofish.v1.OrderCallbackRequest
	12, // 12: admin.service.v1.GoofishApi.GetPlatformInfo:output_type -> goofish.v1.PlatformInfoResponse
	13, // 13: admin.service.v1.GoofishApi.GetUserInfo:output_type -> goofish.v1.UserInfoResponse
	14, // 14: admin.service.v1.GoofishApi.GetGoodsList:output_type -> goofish.v1.GoodsListResponse
	15, // 15: admin.service.v1.GoofishApi.GetGoodsDetail:output_type -> goofish.v1.GoodsDetailResponse
	16, // 16: admin.service.v1.GoofishApi.GetGoodsChangeSubscribeList:output_type -> goofish.v1.GoodsChangeSubscribeListResponse
	17, // 17: admin.service.v1.GoofishApi.GoodsChangeSubscribe:output_type -> goofish.v1.GoodsChangeSubscribeResponse
	18, // 18: admin.service.v1.GoofishApi.GoodsChangeUnsubscribe:output_type -> goofish.v1.GoodsChangeUnsubscribeResponse
	19, // 19: admin.service.v1.GoofishApi.CreateRechargeOrder:output_type -> goofish.v1.CreateRechargeOrderResponse
	20, // 20: admin.service.v1.GoofishApi.CreateCardOrder:output_type -> goofish.v1.CreateCardOrderResponse
	21, // 21: admin.service.v1.GoofishApi.GetOrderDetail:output_type -> goofish.v1.OrderDetailResponse
	22, // 22: admin.service.v1.GoofishApi.GoodsCallback:output_type -> goofish.v1.GoodsCallbackResponse
	23, // 23: admin.service.v1.GoofishApi.OrderCallback:output_type -> goofish.v1.OrderCallbackResponse
	12, // [12:24] is the sub-list for method output_type
	0,  // [0:12] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_admin_service_v1_i_goofish_proto_init() }
func file_admin_service_v1_i_goofish_proto_init() {
	if File_admin_service_v1_i_goofish_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_admin_service_v1_i_goofish_proto_rawDesc), len(file_admin_service_v1_i_goofish_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_admin_service_v1_i_goofish_proto_goTypes,
		DependencyIndexes: file_admin_service_v1_i_goofish_proto_depIdxs,
	}.Build()
	File_admin_service_v1_i_goofish_proto = out.File
	file_admin_service_v1_i_goofish_proto_goTypes = nil
	file_admin_service_v1_i_goofish_proto_depIdxs = nil
}
