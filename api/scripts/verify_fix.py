#!/usr/bin/env python3
"""
验证 OpenAPI 修复结果的脚本

检查关键端点是否正确包含了所需的查询参数
"""

import json
import sys


def verify_openapi_fix():
    """验证 OpenAPI 修复结果"""
    try:
        with open('../../app/admin/service/cmd/server/assets/goofish/openapi.json', 'r', encoding='utf-8') as f:
            doc = json.load(f)
    except FileNotFoundError:
        print("❌ 错误: 找不到 OpenAPI 文档文件")
        return False
    except json.JSONDecodeError:
        print("❌ 错误: OpenAPI 文档格式无效")
        return False

    # 定义预期的查询参数
    expected_params = {
        "/goofish/goods/change/subscribe/list": {
            "query_params": ["mch_id", "timestamp", "sign", "goods_type", "goods_no", "page_no", "page_size"],
            "should_have_body": False
        },
        "/goofish/goods/change/subscribe": {
            "query_params": ["mch_id", "timestamp", "sign"],
            "should_have_body": True
        },
        "/goofish/goods/list": {
            "query_params": ["mch_id", "timestamp", "sign"],
            "should_have_body": True
        },
        "/goofish/user/info": {
            "query_params": ["mch_id", "timestamp", "sign"],
            "should_have_body": False
        }
    }

    all_passed = True
    
    print("🔍 验证 OpenAPI 修复结果...")
    print()

    for path, expected in expected_params.items():
        print(f"📋 检查端点: {path}")
        
        if path not in doc.get("paths", {}):
            print(f"  ❌ 端点不存在")
            all_passed = False
            continue
            
        endpoint = doc["paths"][path].get("post", {})
        
        # 检查查询参数
        actual_params = []
        if "parameters" in endpoint:
            actual_params = [p["name"] for p in endpoint["parameters"] if p.get("in") == "query"]
        
        expected_query_params = expected["query_params"]
        missing_params = set(expected_query_params) - set(actual_params)
        extra_params = set(actual_params) - set(expected_query_params)
        
        if missing_params:
            print(f"  ❌ 缺少查询参数: {list(missing_params)}")
            all_passed = False
        elif extra_params:
            print(f"  ⚠️  额外的查询参数: {list(extra_params)}")
        else:
            print(f"  ✅ 查询参数正确 ({len(actual_params)} 个)")
        
        # 检查 requestBody
        has_body = "requestBody" in endpoint
        should_have_body = expected["should_have_body"]
        
        if has_body and not should_have_body:
            print(f"  ❌ 不应该有 requestBody")
            all_passed = False
        elif not has_body and should_have_body:
            print(f"  ❌ 应该有 requestBody")
            all_passed = False
        else:
            body_status = "有" if has_body else "无"
            print(f"  ✅ requestBody 状态正确 ({body_status})")
        
        print()

    if all_passed:
        print("🎉 所有检查通过！OpenAPI 文档修复成功！")
        print()
        print("✨ 修复摘要:")
        print("  - 所有 Goofish API 端点都包含了必需的查询参数 (mch_id, timestamp, sign)")
        print("  - subscribe/list 端点的所有参数都正确转换为查询参数")
        print("  - 其他端点保留了适当的 requestBody 结构")
        return True
    else:
        print("❌ 发现问题，请检查修复脚本")
        return False


if __name__ == "__main__":
    success = verify_openapi_fix()
    sys.exit(0 if success else 1)
