#!/usr/bin/env python3
import json

# 读取修复后的文档
with open('../../app/admin/service/cmd/server/assets/goofish/openapi.json', 'r', encoding='utf-8') as f:
    doc = json.load(f)

# 检查多个端点
test_paths = [
    "/goofish/goods/change/subscribe/list",
    "/goofish/goods/change/subscribe",
    "/goofish/goods/list",
    "/goofish/user/info"
]

for path in test_paths:
    print(f"\n=== 检查路径: {path} ===")
    if path in doc["paths"]:
        endpoint = doc["paths"][path]["post"]
        if "parameters" in endpoint:
            params = endpoint["parameters"]
            print(f"找到 {len(params)} 个参数:")
            for i, param in enumerate(params):
                print(f"  {i+1}. {param['name']} ({param['schema']['type']})")
        else:
            print("没有找到 parameters")

        if "requestBody" in endpoint:
            print("仍然有 requestBody")
        else:
            print("requestBody 已被删除")
    else:
        print(f"路径 {path} 不存在")
