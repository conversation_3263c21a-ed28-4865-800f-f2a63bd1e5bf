#!/usr/bin/env python3
"""
OpenAPI YAML 文档修复脚本

修复 openapi.yaml 文件中的参数定义问题：
1. 将错误的 "in: path" 参数修正为正确的 "in: query" 或放入 requestBody
2. 确保基础查询参数 (mch_id, timestamp, sign) 正确设置为 "in: query"
3. 将业务参数 (goodsType, goodsNo, pageNo, pageSize) 放入 requestBody
"""

import yaml
import sys
from typing import Dict, List, Any


def fix_subscribe_list_endpoint(endpoint_data: Dict[str, Any]) -> Dict[str, Any]:
    """修复 /goofish/goods/change/subscribe/list 端点"""
    if "parameters" not in endpoint_data:
        return endpoint_data
    
    result = endpoint_data.copy()
    
    # 基础查询参数
    base_query_params = ["mchId", "timestamp", "sign"]
    # 业务参数（应该放在 body 中）
    body_params = ["goodsType", "goodsNo", "pageNo", "pageSize"]
    
    # 处理现有参数
    new_parameters = []
    body_properties = {}
    
    for param in endpoint_data.get("parameters", []):
        param_name = param.get("name", "")
        
        if param_name in base_query_params:
            # 基础参数：修正为 query 参数
            new_param = param.copy()
            new_param["in"] = "query"
            if param_name == "mchId":
                new_param["description"] = "货源平台商户ID（AppKey）"
            elif param_name == "timestamp":
                new_param["description"] = "当前时间戳（单位秒，5分钟内有效）"
                new_param["schema"] = {"type": "integer", "format": "int64"}
            elif param_name == "sign":
                new_param["description"] = "签名MD5值（参考签名说明）"
            new_parameters.append(new_param)
            
        elif param_name in body_params:
            # 业务参数：放入 body
            schema = param.get("schema", {"type": "string"})
            body_properties[param_name] = schema
    
    # 更新参数列表
    result["parameters"] = new_parameters
    
    # 添加 requestBody（如果有业务参数）
    if body_properties:
        result["requestBody"] = {
            "content": {
                "application/json": {
                    "schema": {
                        "type": "object",
                        "properties": body_properties,
                        "required": list(body_properties.keys())
                    }
                }
            },
            "required": True
        }
    
    return result


def fix_other_goofish_endpoints(endpoint_data: Dict[str, Any]) -> Dict[str, Any]:
    """修复其他 Goofish 端点"""
    if "parameters" not in endpoint_data:
        return endpoint_data
    
    result = endpoint_data.copy()
    
    # 基础查询参数
    base_query_params = ["mchId", "timestamp", "sign"]
    
    # 处理现有参数
    new_parameters = []
    
    for param in endpoint_data.get("parameters", []):
        param_name = param.get("name", "")
        
        if param_name in base_query_params:
            # 基础参数：修正为 query 参数
            new_param = param.copy()
            new_param["in"] = "query"
            if param_name == "mchId":
                new_param["description"] = "货源平台商户ID（AppKey）"
            elif param_name == "timestamp":
                new_param["description"] = "当前时间戳（单位秒，5分钟内有效）"
                new_param["schema"] = {"type": "integer", "format": "int64"}
            elif param_name == "sign":
                new_param["description"] = "签名MD5值（参考签名说明）"
            new_parameters.append(new_param)
    
    # 更新参数列表
    result["parameters"] = new_parameters
    
    return result


def fix_openapi_yaml(yaml_data: Dict[str, Any]) -> Dict[str, Any]:
    """修复整个 OpenAPI YAML 文档"""
    result = yaml_data.copy()
    
    if "paths" not in result:
        return result
    
    paths = result["paths"]
    
    for path, path_data in paths.items():
        if not path.startswith("/goofish/"):
            continue
            
        for method, method_data in path_data.items():
            if method.lower() not in ['get', 'post', 'put', 'delete', 'patch']:
                continue
                
            if path == "/goofish/goods/change/subscribe/list":
                # 特殊处理：业务参数放在 body 中
                result["paths"][path][method] = fix_subscribe_list_endpoint(method_data)
            else:
                # 其他端点：只修正基础查询参数
                result["paths"][path][method] = fix_other_goofish_endpoints(method_data)
    
    return result


def main():
    if len(sys.argv) != 3:
        print("使用方法: python fix_openapi_yaml.py <input_file> <output_file>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            yaml_data = yaml.safe_load(f)
        
        fixed_data = fix_openapi_yaml(yaml_data)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            yaml.dump(fixed_data, f, default_flow_style=False, allow_unicode=True, sort_keys=False)
        
        print(f"OpenAPI YAML 文档已修复并保存到: {output_file}")
        
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        sys.exit(1)
    except yaml.YAMLError as e:
        print(f"错误: YAML 解析失败 - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
