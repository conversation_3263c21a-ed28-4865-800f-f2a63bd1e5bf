#!/usr/bin/env python3
"""
OpenAPI 文档修复脚本（简化版）

该脚本用于修复 protoc-gen-openapi 生成的 OpenAPI 文档中的查询参数问题。
它会将应该作为查询参数的字段从 requestBody 移动到 parameters 中。

使用方法:
    python fix_openapi_clean.py <input_file> <output_file>
"""

import json
import sys
import re
from typing import Dict, List, Any


def extract_query_params_from_path(path: str) -> List[str]:
    """从路径中提取查询参数名称"""
    pattern = r'\{([^}]+)\}'
    return re.findall(pattern, path)


def create_parameter_object(param_name: str, param_schema: Dict[str, Any], description: str = "") -> Dict[str, Any]:
    """创建 OpenAPI 参数对象"""
    return {
        "name": param_name,
        "in": "query",
        "required": True,
        "description": description,
        "schema": param_schema
    }


def get_expected_query_params_for_path(path: str) -> List[str]:
    """根据路径返回预期的查询参数列表"""
    goofish_paths = {
        "/goofish/goods/change/subscribe/list": ["mch_id", "timestamp", "sign"],  # 只有基础参数作为查询参数，业务参数放在body
        "/goofish/goods/change/subscribe": ["mch_id", "timestamp", "sign"],
        "/goofish/goods/change/unsubscribe": ["mch_id", "timestamp", "sign"],
        "/goofish/goods/list": ["mch_id", "timestamp", "sign"],
        "/goofish/goods/detail": ["mch_id", "timestamp", "sign"],
        "/goofish/order/recharge/create": ["mch_id", "timestamp", "sign"],
        "/goofish/order/purchase/create": ["mch_id", "timestamp", "sign"],
        "/goofish/order/detail": ["mch_id", "timestamp", "sign"],
        "/goofish/open/info": ["mch_id", "timestamp", "sign"],
        "/goofish/user/info": ["mch_id", "timestamp", "sign"],
    }
    return goofish_paths.get(path, [])


def fix_endpoint(path: str, method_data: Dict[str, Any], schemas: Dict[str, Any], expected_query_params: List[str] = None) -> Dict[str, Any]:
    """修复单个端点的参数定义"""
    if expected_query_params:
        query_params = expected_query_params
    else:
        query_params = extract_query_params_from_path(path)
    
    if not query_params or "requestBody" not in method_data:
        return method_data
    
    request_body = method_data.get("requestBody", {})
    content = request_body.get("content", {})
    json_content = content.get("application/json", {})
    schema_ref = json_content.get("schema", {}).get("$ref", "")
    
    if not schema_ref:
        return method_data
    
    schema_name = schema_ref.split("/")[-1]
    schema = schemas.get(schema_name, {})
    properties = schema.get("properties", {})
    
    parameters = []
    remaining_properties = {}
    processed_params = set()
    
    # 处理每个属性
    for prop_name, prop_schema in properties.items():
        snake_name = re.sub(r'([A-Z])', r'_\1', prop_name).lower().lstrip('_')
        
        if snake_name in query_params:
            param_obj = create_parameter_object(snake_name, prop_schema)
            parameters.append(param_obj)
            processed_params.add(snake_name)
        else:
            remaining_properties[prop_name] = prop_schema
    
    # 添加缺失的基础查询参数
    missing_params = set(query_params) - processed_params
    for param_name in missing_params:
        if param_name == "mch_id":
            param_schema = {"type": "string"}
            description = "货源平台商户ID（AppKey）"
        elif param_name == "timestamp":
            param_schema = {"type": "integer", "format": "int64"}
            description = "当前时间戳（单位秒，5分钟内有效）"
        elif param_name == "sign":
            param_schema = {"type": "string"}
            description = "签名MD5值（参考签名说明）"
        else:
            param_schema = {"type": "string"}
            description = ""
        
        param_obj = create_parameter_object(param_name, param_schema, description)
        parameters.append(param_obj)
    
    result = method_data.copy()
    
    if parameters:
        result["parameters"] = parameters
    
    if remaining_properties:
        new_schema = {
            "type": "object",
            "properties": remaining_properties,
            "description": schema.get("description", "")
        }
        result["requestBody"]["content"]["application/json"]["schema"] = new_schema
    else:
        if "requestBody" in result:
            del result["requestBody"]
    
    return result


def fix_openapi_doc(openapi_doc: Dict[str, Any]) -> Dict[str, Any]:
    """修复整个 OpenAPI 文档"""
    result = openapi_doc.copy()
    paths = result.get("paths", {})
    schemas = result.get("components", {}).get("schemas", {})
    
    for path, path_data in paths.items():
        expected_query_params = get_expected_query_params_for_path(path)
        if expected_query_params:
            for method, method_data in path_data.items():
                if method.lower() in ['get', 'post', 'put', 'delete', 'patch']:
                    result["paths"][path][method] = fix_endpoint(path, method_data, schemas, expected_query_params)
    
    return result


def main():
    if len(sys.argv) != 3:
        print("使用方法: python fix_openapi_clean.py <input_file> <output_file>")
        sys.exit(1)
    
    input_file = sys.argv[1]
    output_file = sys.argv[2]
    
    try:
        with open(input_file, 'r', encoding='utf-8') as f:
            openapi_doc = json.load(f)
        
        fixed_doc = fix_openapi_doc(openapi_doc)
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(fixed_doc, f, indent=2, ensure_ascii=False)
        
        print(f"OpenAPI 文档已修复并保存到: {output_file}")
        
    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误: JSON 解析失败 - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
