#!/usr/bin/env python3
"""
OpenAPI 文档修复脚本

该脚本用于修复 protoc-gen-openapi 生成的 OpenAPI 文档中的查询参数问题。
它会将应该作为查询参数的字段从 requestBody 移动到 parameters 中。

使用方法:
    python fix_openapi.py <input_file> <output_file>
"""

import json
import sys
import re
from typing import Dict, List, Any, Optional


def extract_query_params_from_path(path: str) -> List[str]:
    """从路径中提取查询参数名称"""
    # 匹配 {param_name} 格式的参数
    pattern = r'\{([^}]+)\}'
    return re.findall(pattern, path)


def convert_snake_to_camel(snake_str: str) -> str:
    """将 snake_case 转换为 camelCase"""
    components = snake_str.split('_')
    return components[0] + ''.join(word.capitalize() for word in components[1:])


def create_parameter_object(param_name: str, param_schema: Dict[str, Any], description: str = "") -> Dict[str, Any]:
    """创建 OpenAPI 参数对象"""
    return {
        "name": param_name,
        "in": "query",
        "required": True,
        "description": description,
        "schema": param_schema
    }


def fix_endpoint(path: str, method_data: Dict[str, Any], schemas: Dict[str, Any]) -> Dict[str, Any]:
    """修复单个端点的参数定义"""
    # 提取路径中的查询参数
    query_params = extract_query_params_from_path(path)
    print(f"    提取的查询参数: {query_params}")

    if not query_params or "requestBody" not in method_data:
        print(f"    跳过修复: query_params={bool(query_params)}, requestBody={bool('requestBody' in method_data)}")
        return method_data

    # 获取 requestBody 的 schema 引用
    request_body = method_data.get("requestBody", {})
    content = request_body.get("content", {})
    json_content = content.get("application/json", {})
    schema_ref = json_content.get("schema", {}).get("$ref", "")
    print(f"    Schema 引用: {schema_ref}")

    if not schema_ref:
        print("    没有 schema 引用")
        return method_data

    # 解析 schema 引用
    schema_name = schema_ref.split("/")[-1]
    schema = schemas.get(schema_name, {})
    properties = schema.get("properties", {})
    print(f"    Schema 名称: {schema_name}, 属性数量: {len(properties)}")

    if not properties:
        print("    没有属性")
        return method_data
    
    # 创建参数列表
    parameters = []
    remaining_properties = {}
    processed_params = set()

    # 处理每个属性
    print(f"    处理属性: {list(properties.keys())}")
    for prop_name, prop_schema in properties.items():
        # 将 camelCase 转换为 snake_case 进行匹配
        snake_name = re.sub(r'([A-Z])', r'_\1', prop_name).lower().lstrip('_')
        print(f"      属性 {prop_name} -> {snake_name}, 是否在查询参数中: {snake_name in query_params}")

        if snake_name in query_params:
            # 这是查询参数
            param_obj = create_parameter_object(snake_name, prop_schema)
            parameters.append(param_obj)
            processed_params.add(snake_name)
            print(f"        添加为查询参数: {snake_name}")
        else:
            # 这是 body 参数
            remaining_properties[prop_name] = prop_schema
            print(f"        保留为 body 参数: {prop_name}")

    # 添加缺失的基础查询参数（mch_id, timestamp, sign）
    missing_params = set(query_params) - processed_params
    print(f"    缺失的参数: {missing_params}")
    for param_name in missing_params:
        if param_name == "mch_id":
            param_schema = {"type": "string"}
            description = "货源平台商户ID（AppKey）"
        elif param_name == "timestamp":
            param_schema = {"type": "integer", "format": "int64"}
            description = "当前时间戳（单位秒，5分钟内有效）"
        elif param_name == "sign":
            param_schema = {"type": "string"}
            description = "签名MD5值（参考签名说明）"
        else:
            param_schema = {"type": "string"}
            description = ""

        param_obj = create_parameter_object(param_name, param_schema, description)
        parameters.append(param_obj)
        print(f"        添加缺失的查询参数: {param_name}")
    
    # 更新方法数据
    result = method_data.copy()

    print(f"    最终参数列表长度: {len(parameters)}")
    for i, param in enumerate(parameters):
        print(f"      参数 {i+1}: {param['name']}")

    # 添加参数
    if parameters:
        result["parameters"] = parameters
        print(f"    已添加 {len(parameters)} 个参数到结果中")

    # 如果还有剩余的属性，保留 requestBody；否则删除它
    if remaining_properties:
        print(f"    保留 requestBody，剩余属性: {list(remaining_properties.keys())}")
        # 创建新的 schema 只包含 body 参数
        new_schema = {
            "type": "object",
            "properties": remaining_properties,
            "description": schema.get("description", "")
        }
        result["requestBody"]["content"]["application/json"]["schema"] = new_schema
    else:
        print("    删除 requestBody")
        # 删除 requestBody
        if "requestBody" in result:
            del result["requestBody"]

    return result


def get_expected_query_params_for_path(path: str) -> List[str]:
    """根据路径返回预期的查询参数列表"""
    # 定义需要查询参数的路径和它们的参数
    goofish_paths = {
        "/goofish/goods/change/subscribe/list": ["mch_id", "timestamp", "sign", "goods_type", "goods_no", "page_no", "page_size"],
        "/goofish/goods/change/subscribe": ["mch_id", "timestamp", "sign"],
        "/goofish/goods/change/unsubscribe": ["mch_id", "timestamp", "sign"],
        "/goofish/goods/list": ["mch_id", "timestamp", "sign"],
        "/goofish/goods/detail": ["mch_id", "timestamp", "sign"],
        "/goofish/order/recharge/create": ["mch_id", "timestamp", "sign"],
        "/goofish/order/purchase/create": ["mch_id", "timestamp", "sign"],
        "/goofish/order/detail": ["mch_id", "timestamp", "sign"],
        "/goofish/open/info": ["mch_id", "timestamp", "sign"],
        "/goofish/user/info": ["mch_id", "timestamp", "sign"],
    }

    return goofish_paths.get(path, [])


def fix_openapi_doc(openapi_doc: Dict[str, Any]) -> Dict[str, Any]:
    """修复整个 OpenAPI 文档"""
    result = openapi_doc.copy()
    paths = result.get("paths", {})
    schemas = result.get("components", {}).get("schemas", {})

    print(f"找到 {len(paths)} 个路径")

    for path, path_data in paths.items():
        expected_query_params = get_expected_query_params_for_path(path)
        if expected_query_params:
            print(f"修复路径: {path}, 预期查询参数: {expected_query_params}")
            # 这个路径需要查询参数，需要修复
            for method, method_data in path_data.items():
                if method.lower() in ['get', 'post', 'put', 'delete', 'patch']:
                    print(f"  修复方法: {method}")
                    # 创建一个虚拟路径用于参数提取
                    virtual_path = path + "?" + "&".join([f"{param}={{{param}}}" for param in expected_query_params])
                    print(f"  虚拟路径: {virtual_path}")
                    result["paths"][path][method] = fix_endpoint(virtual_path, method_data, schemas)
        else:
            print(f"跳过路径: {path} (无查询参数)")

    return result


def main():
    if len(sys.argv) != 3:
        print("使用方法: python fix_openapi.py <input_file> <output_file>")
        sys.exit(1)

    input_file = sys.argv[1]
    output_file = sys.argv[2]

    try:
        # 读取输入文件
        with open(input_file, 'r', encoding='utf-8') as f:
            openapi_doc = json.load(f)

        # 修复文档
        fixed_doc = fix_openapi_doc(openapi_doc)

        # 写入输出文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(fixed_doc, f, indent=2, ensure_ascii=False)

        print(f"OpenAPI 文档已修复并保存到: {output_file}")

    except FileNotFoundError:
        print(f"错误: 找不到输入文件 {input_file}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"错误: JSON 解析失败 - {e}")
        sys.exit(1)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
