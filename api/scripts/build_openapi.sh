#!/bin/bash

# OpenAPI 文档构建和修复脚本
# 该脚本会生成 OpenAPI 文档并自动修复查询参数问题

set -e

echo "🚀 开始构建 OpenAPI 文档..."

# 1. 生成原始 OpenAPI 文档
echo "📝 生成原始 OpenAPI 文档..."
cd "$(dirname "$0")/.."
buf generate --template buf.goofish.openapi.gen.yaml

# 2. 修复 OpenAPI 文档
echo "🔧 修复查询参数问题..."
python scripts/fix_openapi_clean.py \
    ../app/admin/service/cmd/server/assets/goofish/openapi.json \
    ../app/admin/service/cmd/server/assets/goofish/openapi.json

echo "✅ OpenAPI 文档构建完成！"
echo ""
echo "📊 修复结果摘要："
cd scripts
python test_fix.py | grep -E "=== 检查路径|找到.*个参数|requestBody"
cd ..

echo ""
echo "📁 生成的文件："
echo "  - app/admin/service/cmd/server/assets/goofish/openapi.json"
echo ""
echo "🎉 完成！"
