#!/usr/bin/env python3
"""
验证 OpenAPI YAML 文档修复结果

检查所有 Goofish API 端点是否正确配置了：
1. 基础查询参数 (mch_id, timestamp, sign) 标记为 "in: query"
2. 业务参数正确放在 requestBody 中
3. 路径名格式正确
"""

import yaml
import sys
from typing import Dict, List, Any


def verify_endpoint(path: str, method_data: Dict[str, Any]) -> Dict[str, Any]:
    """验证单个端点的配置"""
    result = {
        "path": path,
        "status": "✅ 正确",
        "issues": []
    }
    
    # 检查基础查询参数
    base_params = ["mchId", "timestamp", "sign"]
    parameters = method_data.get("parameters", [])
    
    found_query_params = []
    for param in parameters:
        param_name = param.get("name", "")
        param_in = param.get("in", "")
        
        if param_name in base_params:
            if param_in == "query":
                found_query_params.append(param_name)
            else:
                result["issues"].append(f"参数 {param_name} 应该是 'in: query'，但实际是 'in: {param_in}'")
    
    # 检查是否所有基础参数都存在
    missing_params = set(base_params) - set(found_query_params)
    if missing_params:
        result["issues"].append(f"缺少查询参数: {', '.join(missing_params)}")
    
    # 检查 requestBody
    has_request_body = "requestBody" in method_data
    if path == "/goofish/goods/change/subscribe/list":
        # 这个端点应该有 requestBody（包含业务参数）
        if not has_request_body:
            result["issues"].append("缺少 requestBody（应该包含业务参数）")
    elif path in ["/goofish/user/info", "/goofish/open/info"]:
        # 这些端点可能没有 requestBody
        pass
    else:
        # 其他端点应该有 requestBody（包含业务参数）
        if not has_request_body:
            result["issues"].append("缺少 requestBody（应该包含业务参数）")
    
    # 检查路径格式
    if "?" in path or "{" in path:
        result["issues"].append(f"路径格式不正确，包含查询参数或模板变量: {path}")
    
    if result["issues"]:
        result["status"] = "❌ 有问题"
    
    return result


def verify_openapi_yaml(yaml_file: str) -> None:
    """验证整个 OpenAPI YAML 文档"""
    try:
        with open(yaml_file, 'r', encoding='utf-8') as f:
            yaml_data = yaml.safe_load(f)
    except FileNotFoundError:
        print(f"❌ 错误: 找不到文件 {yaml_file}")
        return
    except yaml.YAMLError as e:
        print(f"❌ 错误: YAML 解析失败 - {e}")
        return
    
    if "paths" not in yaml_data:
        print("❌ 错误: OpenAPI 文档中没有 paths 部分")
        return
    
    print("🔍 验证 Goofish API 端点配置...")
    print("=" * 60)
    
    goofish_endpoints = []
    paths = yaml_data["paths"]
    
    for path, path_data in paths.items():
        if not path.startswith("/goofish/"):
            continue
            
        for method, method_data in path_data.items():
            if method.lower() not in ['get', 'post', 'put', 'delete', 'patch']:
                continue
                
            result = verify_endpoint(path, method_data)
            goofish_endpoints.append(result)
    
    # 显示结果
    success_count = 0
    for endpoint in goofish_endpoints:
        print(f"{endpoint['status']} {endpoint['path']}")
        if endpoint['issues']:
            for issue in endpoint['issues']:
                print(f"   - {issue}")
        else:
            success_count += 1
        print()
    
    # 总结
    total_count = len(goofish_endpoints)
    print("=" * 60)
    print(f"📊 验证结果: {success_count}/{total_count} 个端点配置正确")
    
    if success_count == total_count:
        print("🎉 所有 Goofish API 端点都已正确配置！")
    else:
        print(f"⚠️  还有 {total_count - success_count} 个端点需要修复")
    
    # 特别检查 subscribe/list 端点
    print("\n🔍 特别检查 /goofish/goods/change/subscribe/list 端点:")
    subscribe_list_endpoint = None
    for endpoint in goofish_endpoints:
        if endpoint['path'] == '/goofish/goods/change/subscribe/list':
            subscribe_list_endpoint = endpoint
            break
    
    if subscribe_list_endpoint:
        if not subscribe_list_endpoint['issues']:
            print("✅ subscribe/list 端点配置正确:")
            print("   - 基础参数 (mchId, timestamp, sign) 作为查询参数")
            print("   - 业务参数 (goodsType, goodsNo, pageNo, pageSize) 在 requestBody 中")
        else:
            print("❌ subscribe/list 端点配置有问题:")
            for issue in subscribe_list_endpoint['issues']:
                print(f"   - {issue}")
    else:
        print("❌ 未找到 subscribe/list 端点")


def main():
    if len(sys.argv) != 2:
        print("使用方法: python verify_yaml_fix.py <openapi_yaml_file>")
        sys.exit(1)
    
    yaml_file = sys.argv[1]
    verify_openapi_yaml(yaml_file)


if __name__ == "__main__":
    main()
